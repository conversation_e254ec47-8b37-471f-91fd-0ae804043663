import React, { useRef } from 'react';
import { Typography, Icon, IconCard } from '@/shared/components/common';

interface TicketImageUploaderProps {
  /**
   * URL ảnh hiện tại
   */
  value?: string;
  
  /**
   * Callback khi thay đổi ảnh
   */
  onChange: (imageUrl: string) => void;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component upload ảnh vé với preview và các nút thay đổi/xóa
 */
const TicketImageUploader: React.FC<TicketImageUploaderProps> = ({
  value,
  onChange,
  placeholder = 'Chưa có hình ảnh vé',
  className = '',
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Xử lý khi chọn file
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      
      if (!file.type.startsWith('image/')) {
        alert('Vui lòng chọn file ảnh');
        return;
      }

      // Tạo URL tạm thời cho preview
      const imageUrl = URL.createObjectURL(file);
      onChange(imageUrl);
    }
  };

  // Xử lý khi click nút thay đổi ảnh
  const handleChangeImage = () => {
    fileInputRef.current?.click();
  };

  // Xử lý khi click nút xóa ảnh
  const handleRemoveImage = () => {
    onChange('');
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Preview area */}
      <div className="relative">
        {value ? (
          <div className="w-full h-32 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-800">
            <img
              src={value}
              alt="Ticket preview"
              className="w-full h-full object-cover"
            />
          </div>
        ) : (
          <div className="w-full h-32 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-800">
            <Icon name="image" size="md" className="mb-2 text-gray-400" />
            <Typography variant="caption" className="text-gray-500 dark:text-gray-400 text-center">
              {placeholder}
            </Typography>
          </div>
        )}
      </div>

      {/* Action buttons */}
      <div className="flex items-center space-x-2">
        <IconCard
          icon="upload"
          title={value ? "Thay đổi hình ảnh" : "Tải lên hình ảnh"}
          onClick={handleChangeImage}
          variant="secondary"
          size="sm"
          className="cursor-pointer"
        />
        
        {value && (
          <IconCard
            icon="trash"
            title="Xóa hình ảnh"
            onClick={handleRemoveImage}
            variant="secondary"
            size="sm"
            className="cursor-pointer text-red-500 hover:text-red-600"
          />
        )}
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

export default TicketImageUploader;
