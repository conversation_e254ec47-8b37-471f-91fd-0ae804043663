import React, { useRef } from 'react';
import { Typography, Icon, IconCard } from '@/shared/components/common';

interface TicketImageUploaderProps {
  /**
   * URL ảnh hiện tại
   */
  value?: string;
  
  /**
   * Callback khi thay đổi ảnh
   */
  onChange: (imageUrl: string) => void;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component upload ảnh vé với preview và các nút thay đổi/xóa
 */
const TicketImageUploader: React.FC<TicketImageUploaderProps> = ({
  value,
  onChange,
  placeholder = 'Chưa có hình ảnh vé',
  className = '',
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Xử lý khi chọn file
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      
      if (!file.type.startsWith('image/')) {
        alert('Vui lòng chọn file ảnh');
        return;
      }

      // Tạo URL tạm thời cho preview
      const imageUrl = URL.createObjectURL(file);
      onChange(imageUrl);
    }
  };

  // Xử lý khi click nút thay đổi ảnh
  const handleChangeImage = () => {
    fileInputRef.current?.click();
  };

  // Xử lý khi click nút xóa ảnh
  const handleRemoveImage = () => {
    onChange('');
  };

  return (
    <div className={`${className}`}>
      {/* Preview area with overlay buttons */}
      <div className="relative group">
        {value ? (
          <div className="w-full h-32 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-800 relative">
            <img
              src={value}
              alt="Ticket preview"
              className="w-full h-full object-cover"
            />
            {/* Overlay with action buttons */}
            <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center space-x-2">
              <button
                onClick={handleChangeImage}
                className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 transition-all duration-200 shadow-lg"
                title="Thay đổi hình ảnh"
              >
                <Icon name="upload" size="sm" className="text-gray-700" />
              </button>
              <button
                onClick={handleRemoveImage}
                className="bg-red-500 bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 transition-all duration-200 shadow-lg"
                title="Xóa hình ảnh"
              >
                <Icon name="trash" size="sm" className="text-white" />
              </button>
            </div>
          </div>
        ) : (
          <div
            className="w-full h-32 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-800 hover:border-primary hover:bg-primary/5 transition-colors cursor-pointer"
            onClick={handleChangeImage}
          >
            <Icon name="upload" size="md" className="mb-2 text-gray-400" />
            <Typography variant="caption" className="text-gray-500 dark:text-gray-400 text-center">
              {placeholder}
            </Typography>
          </div>
        )}
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

export default TicketImageUploader;
